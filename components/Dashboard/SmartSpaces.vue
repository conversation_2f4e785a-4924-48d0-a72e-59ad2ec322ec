<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui'
import { useListingStore, useWorkspaceStore } from '#imports'

const workspaceStore = useWorkspaceStore()
const listingStore = useListingStore()

const columns: TableColumn<any>[] = [
  {
    accessorKey: 'name',
    header: 'Property Name',
    cell: ({ row }) => {
      return h('span', {
        style: 'color: #2563eb;',
      }, row.original.name)
    },
  },
  {
    accessorKey: 'cardId',
    header: 'Card ID',
    cell: ({ row }) => row.original.cardId,
  },
  {
    accessorKey: 'scans',
    header: 'Scans',
    cell: ({ row }) => row.original.scans.toString(),
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => row.original.date,
  },
]

const data = computed(() => {
  try {
    if (!workspaceStore.smartSpaces) {
      console.log('no smart spaces yet')
      return {
        totalScans: 0,
        popularCards: [],
        popularListings: [],
      }
    }

    // Get aggregate metrics with null check
    const aggregateMetrics = workspaceStore.smartSpaces.find(item => item?.data_type === 'aggregate_metrics') || {}

    // Get sub source breakdown for pie chart with error handling
    const cardIdBreakdown = (workspaceStore.smartSpaces || [])
      .filter(item => item?.data_type === 'card_id_breakdown' && item?.card_id !== null)
      .map(item => ({
        scans: Number.parseInt(item.card_id_count) || 0,
        name: (item.card_id || 'Unknown')
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' '),
      }))
      .filter(item => Number.isNaN(item.scans) === false) // Filter out invalid scan counts

    // Get last 10 scans sorted by timestamp with error handling
    const recentScans = (workspaceStore.smartSpaces || [])
      .filter(item => item?.data_type === 'last_10_scans' && item?.__timestamp)
      .sort((a, b) => {
        try {
          return new Date(b.__timestamp) - new Date(a.__timestamp)
        }
        catch {
          return 0
        }
      })
      .slice(0, 10)
      .map((item) => {
        const listing = listingStore.listings?.find(l => l?.id === item.listing_id)
        return {
          name: listing?.name || 'Unknown Listing',
          cardId: item.card_id || 'N/A',
          scans: 1,
          date: new Date(item.__timestamp).toLocaleDateString(),
        }
      })

    return {
      totalScans: Number.parseInt(aggregateMetrics.total_rows) || 0,
      popularCards: cardIdBreakdown,
      popularListings: recentScans,
    }
  }
  catch (err) {
    console.error('Error in data computed property:', err)
    return {
      totalScans: 0,
      popularCards: [],
      popularListings: [],
    }
  }
})

const stats = computed(() => {
  try {
    // Get sub source counts with null checks
    const coverScans = workspaceStore.smartSpaces
      ?.find(item => item?.data_type === 'sub_src_breakdown' && item?.sub_src === 'cover')
      ?.sub_src_count || '0'
    const amenityScans = workspaceStore.smartSpaces
      ?.find(item => item?.data_type === 'sub_src_breakdown' && item?.sub_src === 'amenity')
      ?.sub_src_count || '0'

    return [
      {
        title: 'Total Scans',
        value: formatNumber(data.value.totalScans),
        icon: 'pi pi-qrcode',
      },
      {
        title: 'Cover Scans',
        value: formatNumber(Number.parseInt(coverScans) || 0),
        icon: 'pi pi-image',
      },
      {
        title: 'Amenity Scans',
        value: formatNumber(Number.parseInt(amenityScans) || 0),
        icon: 'pi pi-list',
      },
    ]
  }
  catch (e) {
    console.error('Error in metrics computed property:', e)
    return []
  }
})

const donutData = computed(() => {
  return data.value.popularCards.map(card => card?.scans || 0)
})

const chartLabels = computed(() => {
  // Define a set of colors to cycle through
  const colors = ['#3b82f6', '#22c55e', '#f59e0b', '#a855f7', '#06b6d4', '#8b5cf6', '#ef4444', '#84cc16']

  return data.value.popularCards.map((card, index) => ({
    name: card?.name || 'Unknown',
    color: colors[index % colors.length],
  }))
})

function formatNumber(num) {
  try {
    return (num || 0).toLocaleString()
  }
  catch (e) {
    console.error('Error formatting number:', e)
    return '0'
  }
}
</script>

<template>
  <div>
    <UCard class="!bg-(--ui-bg)">
      <template #header>
        <div class="mb-2">
          <h2 class="text-lg font-medium">
            Smart Spaces
          </h2>
          <p class="text-sm text-(--ui-text-muted)">
            Statistics and insights about your smart spaces usage over the last 30 days.
          </p>
        </div>
        <div class="grid grid-cols-3 gap-2">
          <div v-for="stat, index in stats" :key="index" class="border border-gray-200 rounded-lg p-1">
            <div class="flex items-center gap-2">
              <div class="flex-shrink-0">
                <i :class="stat.icon" class="text-xl text-(--ui-primary)" />
              </div>
              <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-(--ui-text) truncate">
                  {{ stat.title }}
                </h3>
                <p class="text-lg font-semibold text-(--ui-text)">
                  {{ stat.value }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-2">
        <div class="flex flex-col items-center">
          <DonutChart :data="donutData" :height="200" :labels="chartLabels" :hide-legend="true" :radius="0" />
          <div class="mt-2 flex flex-wrap gap-2 justify-center max-w-xs">
            <div v-for="label in chartLabels" :key="label.name" class="flex items-center gap-1 text-xs">
              <div class="w-2 h-2 rounded-full flex-shrink-0" :style="{ backgroundColor: label.color }" />
              <span class="text-gray-600 truncate">{{ label.name }}</span>
            </div>
          </div>
        </div>
        <div>
          <UTable :data="data.popularListings" :columns="columns" style="height: 350px;" />
        </div>
      </div>
    </UCard>
  </div>
</template>
