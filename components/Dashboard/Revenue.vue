<script setup lang="ts">
import { useWorkspaceStore } from '#imports'

const workspaceStore = useWorkspaceStore()



const stats = computed(() => {
  const conversions = workspaceStore.conversions || []
  const winbackConversions = conversions.filter(c => c.type === 'inquiryCartAbandonment' && c.succeeded)

  const upsellTypes: any = {
    earlyCheckinOffer: 40,
    lateCheckoutOffer: 40,
    preStayExtraNightOffer: 230,
    postStayExtraNightOffer: 230,
  }

  const upsellConversions = conversions.filter(c => Object.keys(upsellTypes).includes(c.type) && c.succeeded)

  const winbackTotal = winbackConversions.reduce((sum, c) => sum + (Number(c.amount) || 0), 0)
  const upsellTotal = upsellConversions.reduce((sum, c) => {
    const amount = Number(c.amount)
    return sum + ((!Number.isNaN(amount) && amount > 0) ? amount : upsellTypes[c.type])
  }, 0)

  const totalRevenue = winbackTotal + upsellTotal
  const successRate = ((winbackConversions.length + upsellConversions.length) / (conversions.length || 1) * 100)

  return [
    {
      title: 'Total Revenue',
      value: `$${formatMoney(totalRevenue)}`,
      change: 'From Winbacks & Upsells',
      icon: 'pi pi-dollar',
    },
    {
      title: 'Winback Revenue',
      value: `$${formatMoney(winbackTotal)}`,
      change: `${winbackConversions.length} successful winbacks`,
      icon: 'pi pi-refresh',
    },
    {
      title: 'Upsell Revenue',
      value: `$${formatMoney(upsellTotal)}`,
      change: `${upsellConversions.length} successful upsells`,
      icon: 'pi pi-chart-bar',
    },
    {
      title: 'Success Rate',
      value: `${successRate.toFixed(1)}%`,
      change: 'Conversion rate',
      icon: 'pi pi-check-circle',
    },
  ]
})

const topAutomations = computed(() => {
  const conversions = workspaceStore.conversions || []
  const automationTypes = {
    inquiryCartAbandonment: { name: 'Winback', icon: 'pi pi-refresh' },
    earlyCheckinOffer: { name: 'Early Check-in', icon: 'pi pi-clock', defaultAmount: 40 },
    lateCheckoutOffer: { name: 'Late Check-out', icon: 'pi pi-clock', defaultAmount: 40 },
    preStayExtraNightOffer: { name: 'Pre-stay Night', icon: 'pi pi-calendar-plus', defaultAmount: 230 },
    postStayExtraNightOffer: { name: 'Post-stay Night', icon: 'pi pi-calendar-plus', defaultAmount: 230 },
  }

  const stats = Object.entries(automationTypes).map(([type, info]) => {
    const typeConversions = conversions.filter(c => c.type === type)
    const succeeded = typeConversions.filter(c => c.succeeded)

    if (typeConversions.length === 0)
      return null

    const conversionRate = (succeeded.length / typeConversions.length) * 100
    const totalAmount = succeeded.reduce((sum, c) => {
      const amount = Number(c.amount)
      return sum + ((!Number.isNaN(amount) && amount > 0) ? amount : (info.defaultAmount || 0))
    }, 0)

    return {
      name: info.name,
      conversionRate: `${conversionRate.toFixed(1)}%`,
      amount: `$${formatMoney(totalAmount)}`,
      icon: info.icon,
    }
  }).filter(Boolean) // Remove null entries

  // Sort by conversion rate (descending)
  return stats.sort((a, b) =>
    Number.parseFloat(b.conversionRate) - Number.parseFloat(a.conversionRate),
  ).slice(0, 3) // Take top 3
})

function formatMoney(amount) {
  return amount.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })
}
</script>

<template>
  <div>
    <UCard class="!bg-(--ui-bg)">
      <template #header>
        <div class="mb-2">
          <h2 class="text-lg font-medium">
            Yada Attributed Revenue
          </h2>
          <p class="text-sm text-(--ui-text-muted)">
            Upsell and Booking Buybacks revenue generated by Yada over the last 30 days.
          </p>
        </div>
        <div class="grid grid-cols-3 gap-2">
          <div v-for="stat, index in stats" :key="index" class="border border-gray-200 rounded-lg p-1">
            <div class="flex items-center gap-2">
              <div class="flex-shrink-0">
                <i :class="stat.icon" class="text-xl text-(--ui-primary)" />
              </div>
              <div class="min-w-0 flex-1">
                <h3 class="text-sm font-medium text-(--ui-text) truncate">
                  {{ stat.title }}
                </h3>
                <p class="text-lg font-semibold text-(--ui-text)">
                  {{ stat.value }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </template>
      <UTable :data="topAutomations" />
      <template #footer>
        <div class="flex justify-end gap-2">
          <UButton variant="link" trailing-icon="i-lucide-external-link" color="neutral" to="/conversions">
            View Conversions
          </UButton>
        </div>
      </template>
    </UCard>
  </div>
</template>
