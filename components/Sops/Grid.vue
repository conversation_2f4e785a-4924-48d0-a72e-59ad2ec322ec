<script setup lang="ts">
const props = defineProps<{ sops: Sop[] }>()
const emit = defineEmits(['view-sop'])
import type { Sop } from '~/types'

function formatDate(value: Sop['createdAt']) {
  if (!value) return '—'
  let date: Date | undefined
  if (typeof value === 'object' && value._seconds) {
    date = new Date(value._seconds * 1000)
  } else if (typeof value === 'number') {
    date = new Date(value)
  }
  return date ? date.toLocaleDateString() : '—'
}
</script>

<template>
  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
    <UCard v-for="sop in sops" :key="sop.id" class="hover:shadow-md transition-shadow">
      <template #header>
        <div class="text-base font-medium text-primary cursor-pointer underline" @click="$emit('view-sop', sop)">
          {{ sop.title }}
        </div>
      </template>
      <p class="text-sm text-muted-foreground mb-2">{{ sop.description }}</p>
      <div class="flex justify-between text-xs text-muted-foreground mt-4">
        <span>Type: {{ sop.type || '—' }}</span>
        <span>Created: {{ formatDate(sop.createdAt) }}</span>
      </div>
    </UCard>
  </div>
</template>

<style scoped>
.text-primary {
  color: #2563eb;
}

.text-muted-foreground {
  color: #6b7280;
}
</style>
