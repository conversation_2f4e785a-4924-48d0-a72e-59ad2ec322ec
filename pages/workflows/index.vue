<script setup lang="ts">
import { ref, onMounted } from 'vue'

definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const tabItems = [
  {
    slot: 'active', // Corresponds to the <template #active>
    label: 'Active',
    value: 'active', // Added for clarity and potential use with default-value on UTabs
  },
  {
    slot: 'drafts', // Corresponds to the <template #drafts>
    label: 'Drafts',
    value: 'drafts',
  },
]

const workflows = ref<any[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

async function fetchWorkflows() {
  loading.value = true
  error.value = null
  try {
    workflows.value = await useApiFetch('workflows', {
      method: 'GET',
    })
  } catch (e: any) {
    error.value = e.message || 'Failed to fetch workflows'
  } finally {
    loading.value = false
  }
}

onMounted(fetchWorkflows)

function formatLastUpdated(ts?: any) {
  if (!ts) return ''
  // If Firestore timestamp, convert to Date
  if (ts.seconds) {
    const date = new Date(ts.seconds * 1000)
    return date.toLocaleDateString()
  }
  // Otherwise, try to parse as string/date
  return new Date(ts).toLocaleDateString()
}
</script>

<template>
  <UDashboardPanel id="workflows">
    <template #header>
      <UDashboardNavbar title="Workflows">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <!-- ListingsAddModal can be added here in the future -->
          <div class="flex items-center gap-2">
            <NuxtLink to="/workflows/new">
              <UButton icon="i-heroicons-plus" label="Create Workflow" />
            </NuxtLink>
            <NuxtLink to="/workflows/calendar">
              <UButton icon="i-heroicons-calendar-days" label="Calendar" variant="outline" />
            </NuxtLink>
          </div>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <UTabs :items="tabItems" class="space-y-4" default-value="active">
        <template #active="{ item }">
          <div class="space-y-4">
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <h2 class="text-lg font-semibold">
                    Today's Scheduled Messages
                  </h2>
                  <NuxtLink to="/workflows/calendar">
                    <UButton variant="outline" size="sm" icon="i-heroicons-calendar-days" label="View Calendar" />
                  </NuxtLink>
                </div>
              </template>
              <WorkflowsTodayRuns />
            </UCard>

            <div class="grid gap-4 md:grid-cols-3">
              <UCard>
                <template #header>
                  <h3 class="text-sm font-medium">
                    Active Workflows
                  </h3>
                </template>
                <div class="text-2xl font-bold">
                  {{ workflows.filter(w => w.active).length }}
                </div>
              </UCard>
              <UCard>
                <template #header>
                  <h3 class="text-sm font-medium">
                    Guest Messages
                  </h3>
                </template>
                <div>
                  <div class="text-2xl font-bold">
                    128
                  </div>
                  <p class="text-xs text-muted-foreground">
                    This month
                  </p>
                </div>
              </UCard>
              <UCard>
                <template #header>
                  <h3 class="text-sm font-medium">
                    Extra Revenue
                  </h3>
                </template>
                <div>
                  <div class="text-2xl font-bold">
                    $1,240
                  </div>
                  <p class="text-xs text-muted-foreground">
                    This month
                  </p>
                </div>
              </UCard>
            </div>

            <h2 class="text-xl font-semibold mt-6">
              Your Workflows
            </h2>
            <div v-if="loading" class="text-center py-8">Loading workflows...</div>
            <div v-else-if="error" class="text-center text-red-500 py-8">{{ error }}</div>
            <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <WorkflowsWorkflowCard v-for="w in workflows.filter(w => w.active)" :key="w.id" :title="w.name"
                :description="w.description" :status="w.active ? 'active' : 'draft'"
                :last-updated="formatLastUpdated(w.updatedAt || w.createdAt)" simple :id="w.id" />
            </div>
          </div>
        </template>

        <template #drafts="{ item }">
          <div class="space-y-4">
            <h2 class="text-xl font-semibold">
              Draft Workflows
            </h2>
            <div v-if="loading" class="text-center py-8">Loading workflows...</div>
            <div v-else-if="error" class="text-center text-red-500 py-8">{{ error }}</div>
            <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <WorkflowsWorkflowCard v-for="w in workflows.filter(w => !w.active)" :key="w.id" :title="w.name"
                :description="w.description" :status="'draft'"
                :last-updated="formatLastUpdated(w.updatedAt || w.createdAt)" simple />
            </div>
          </div>
        </template>
      </UTabs>
    </template>
  </UDashboardPanel>
</template>

<style scoped></style>
