<script setup lang="ts">
import { useListingStore } from '#imports'
import type { RadioGroupItem } from '@nuxt/ui'
import { ref, computed, watch } from 'vue'
import {
  getWorkflowFromTemplate,
  addMessageStep,
  deleteLastMessageStep,
  canAddStepByType,
  parseDuration,
  formatDuration,
} from '~/shared/templates/workflows/utils'


definePageMeta({
  layout: 'loggedin',
  middleware: 'authguard',
})

const listingStore = useListingStore()

const toast = useToast()

const workflow = ref<any | null>(null)

// Define step interface
interface WorkflowStep {
  id: string
  type: string
  subject?: string
  body?: string
  duration?: string
  durationValue?: number
  durationUnit?: 'hours' | 'days'
}

// Reactive form data
const formData = ref({
  name: '',
  description: '',
  type: 'upsell',
  trigger: 'checkin',
  triggerTiming: {
    when: 'before',
    time: '3d'
  },
  active: true,
  sendEmail: true,
  sendSms: true,
  sendOta: true,
  steps: [] as WorkflowStep[],
  listingIds: [] as string[],
})

// Watch for workflow type changes and adjust steps accordingly
watch(() => formData.value.type, (newType, oldType) => {
  if (newType === 'upsell' && oldType === 'recovery' && formData.value.steps.length > 1) {
    // When switching from recovery to upsell, keep only the first message step
    const firstMessageStep = formData.value.steps.find(step => step.type === 'message')
    if (firstMessageStep) {
      formData.value.steps = [firstMessageStep]
      // Also update the workflow object if it exists
      if (workflow.value) {
        const newSteps = { 1: { ...firstMessageStep } }
        workflow.value.steps = newSteps
      }
      toast.add({
        title: 'Steps Adjusted',
        description: 'Upsell workflows are limited to 1 message step. Additional steps have been removed.',
        color: 'warning',
      })
    }
  }

  // Reset trigger if it's not valid for the new workflow type
  if (newType === 'recovery') {
    // Recovery workflows only use inquiry trigger
    formData.value.trigger = 'inquiry'
  } else {
    // Upsell workflows use checkin/checkout triggers
    if (formData.value.trigger === 'inquiry') {
      formData.value.trigger = 'checkin'
      formData.value.triggerTiming.when = 'before'
      formData.value.triggerTiming.time = '3d'
    }
  }
})

// Computed property to check if the workflow is valid
const isWorkflowValid = computed(() => {
  return validateWorkflow().length === 0
})

// Options for URadioGroup and USelectMenu
const workflowTypeOptions = ref<RadioGroupItem[]>([
  { label: 'Recovery', value: 'recovery' },
  { label: 'Upsell', value: 'upsell' },
  // { label: 'Communication', value: 'communication' }
])

// Computed trigger options based on workflow type
const triggerOptions = computed(() => {
  if (formData.value.type === 'recovery') {
    // Recovery workflows only show inquiry trigger
    return [
      { value: 'inquiry', label: 'When Inquiry Received' },
    ]
  } else {
    // Upsell workflows show checkin/checkout triggers
    return [
      { value: 'checkin', label: 'Check In' },
      { value: 'checkout', label: 'Check Out' },
    ]
  }
})

// Computed timing options (before/after)
const timingOptions = [
  { value: 'before', label: 'Before' },
  { value: 'after', label: 'After' },
]

// Computed property to get/set days from triggerTiming.time
const triggerDays = computed({
  get: () => {
    if (!formData.value.triggerTiming?.time) return 3
    const match = formData.value.triggerTiming.time.match(/^(\d+)d$/)
    return match ? parseInt(match[1], 10) : 3
  },
  set: (value: number) => {
    if (formData.value.triggerTiming) {
      formData.value.triggerTiming.time = `${value}d`
    }
  }
})

// Listing items for the multi-select
const listingItems = computed(() => [
  { label: 'Select All', value: 'select-all', special: true },
  ...listingStore.listings.map((l: any) => ({ label: l.name, value: l.id })),
])

// Handle listing selection with "Select All" functionality
function handleListingSelection(selectedValues: string[]) {
  if (selectedValues.includes('select-all')) {
    // If "Select All" is selected, select all actual listings
    if (formData.value.listingIds.length === listingStore.listings.length) {
      // If all are already selected, deselect all
      formData.value.listingIds = []
    } else {
      // Select all listings
      formData.value.listingIds = listingStore.listings.map((l: any) => l.id)
    }
  } else {
    // Normal selection without "Select All"
    formData.value.listingIds = selectedValues.filter(v => v !== 'select-all')
  }
}

// START: Added Name Template Options and Function
const nameTemplateOptions = [
  [
    { label: 'Inquiry Winback' },
    { label: 'Early Check In Upsell' },
    { label: 'Late Check Out Upsell' },
    { label: 'Pre Stay Extra Night Upsell' },
    { label: 'Post Stay Extra Night Upsell' },
  ],
  // You can add more groups of items if needed, separated by arrays
  // e.g., [{ label: 'Another Option' }]
]

function handleTemplateSelect(templateName: string) {
  let type: 'winback' | 'upsell' | null = null
  if (templateName.toLowerCase().includes('winback')) type = 'winback'
  if (templateName.toLowerCase().includes('upsell')) type = 'upsell'
  if (!type) return
  workflow.value = getWorkflowFromTemplate(templateName)
  // Populate formData fields from workflow
  formData.value.name = workflow.value.name
  formData.value.description = workflow.value.description
  formData.value.type = workflow.value.type === 'winback' ? 'recovery' : 'upsell'

  // Map template trigger
  formData.value.trigger = workflow.value.trigger

  // Only set triggerTiming if it exists (inquiry triggers don't have it)
  if (workflow.value.triggerTiming) {
    formData.value.triggerTiming = { ...workflow.value.triggerTiming }
  } else if (workflow.value.trigger === 'inquiry') {
    // For inquiry triggers, we don't need triggerTiming
    // Keep the existing default values for when user switches to other triggers
  }

  formData.value.steps = stepsArrayFromWorkflow(workflow.value)
}

function stepsArrayFromWorkflow(wf: any): WorkflowStep[] {
  // Convert steps object to array with id/type/subject/body/duration
  return Object.entries(wf.steps).map(([id, step]: any) => {
    const baseStep = {
      id,
      ...step
    }

    // If it's a delay step with duration, parse it into durationValue and durationUnit
    if (step.type === 'delay' && step.duration) {
      try {
        const { value, unit } = parseDuration(step.duration)
        baseStep.durationValue = value
        baseStep.durationUnit = unit
      } catch (error) {
        console.warn(`Failed to parse duration "${step.duration}":`, error)
        // Fallback to default values
        baseStep.durationValue = 1
        baseStep.durationUnit = 'days'
      }
    }

    return baseStep
  })
}

function syncFormSteps() {
  if (workflow.value) {
    formData.value.steps = stepsArrayFromWorkflow(workflow.value)
  }
}

function handleAddStep() {
  if (workflow.value) {
    // Check if we can add more steps based on workflow type
    const messageSteps = formData.value.steps.filter(step => step.type === 'message')
    if (canAddStepByType(formData.value.type, messageSteps.length)) {
      addMessageStep(workflow.value)
      syncFormSteps()
    } else {
      toast.add({
        title: 'Cannot Add Step',
        description: 'Upsell workflows are limited to 1 message step',
        color: 'error',
      })
    }
  }
}

function handleRemoveStep() {
  if (workflow.value) {
    deleteLastMessageStep(workflow.value)
    syncFormSteps()
  }
}

function handleUpdateStep({ id, field, value }: { id: string, field: string, value: string | number }) {
  if (!workflow.value) return
  if (workflow.value.steps[id]) {
    // Handle duration-related updates specially
    if (field === 'durationValue' || field === 'durationUnit') {
      // Find the current step in formData to get both value and unit
      const currentStep = formData.value.steps.find(step => step.id === id)
      if (currentStep && currentStep.type === 'delay') {
        // Update the field in formData first
        if (field === 'durationValue') {
          currentStep.durationValue = value as number
        } else {
          currentStep.durationUnit = value as 'hours' | 'days'
        }

        // Convert back to duration string for the workflow object
        if (currentStep.durationValue && currentStep.durationUnit) {
          const durationString = formatDuration(currentStep.durationValue, currentStep.durationUnit)
          workflow.value.steps[id].duration = durationString
        }
      }
    } else {
      // Handle other fields normally
      workflow.value.steps[id][field] = value
    }
    syncFormSteps()
  }
}
// END: Added Name Template Options and Function

function validateWorkflow() {
  const errors: string[] = []

  // Check title length
  if (!formData.value.name || formData.value.name.trim().length < 5) {
    errors.push('Workflow name must be at least 5 characters')
  }

  // Check description length
  if (!formData.value.description || formData.value.description.trim().length < 5) {
    errors.push('Description must be at least 5 characters')
  }

  // Check at least 1 listing
  if (formData.value.listingIds.length === 0) {
    errors.push('At least 1 listing must be selected')
  }

  // Check at least 1 message step
  const messageSteps = formData.value.steps.filter(step => step.type === 'message')
  if (messageSteps.length === 0) {
    errors.push('At least 1 message step is required')
  }

  // Check at least one communication channel
  if (!formData.value.sendEmail && !formData.value.sendSms && !formData.value.sendOta) {
    errors.push('At least one communication channel (Email, SMS, or OTA) must be selected')
  }

  return errors
}

async function handleSave() {
  // Validate the workflow before saving
  const validationErrors = validateWorkflow()

  if (validationErrors.length > 0) {
    toast.add({
      title: 'Validation Error',
      description: `Please fix the following issues: ${validationErrors.join(', ')}`,
      color: 'error',
    })
    return
  }

  try {
    // Prepare the data to send - exclude triggerTiming for inquiry triggers
    const { triggerTiming, ...baseData } = formData.value
    const dataToSend = formData.value.trigger === 'inquiry'
      ? baseData
      : formData.value

    await useApiFetch('workflows', {
      method: 'POST',
      body: dataToSend,
    })

    toast.add({
      title: 'Success',
      description: 'Workflow created successfully',
      color: 'success',
    })

    // Navigate back to workflows list
    await navigateTo('/workflows')
  }
  catch (error) {
    console.error('Failed to create workflow:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to create workflow',
      color: 'error',
    })
  }
}
</script>

<template>
  <UDashboardPanel id="workflow-new">
    <template #header>
      <UDashboardNavbar title="Create New Workflow">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
        </template>
      </UDashboardNavbar>
    </template>
    <template #body>
      <div class="grid w-full gap-6">
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Basic Information
            </h3>
          </template>

          <div class="space-y-4">
            <div class="mb-2 flex justify-end">
              <UDropdownMenu :items="nameTemplateOptions" :popper="{ placement: 'bottom-start' }">
                <UButton color="primary" label="Start with Template" trailing-icon="i-heroicons-chevron-down-20-solid" />

                <template #item="{ item }">
                  <span class="truncate" @click="handleTemplateSelect(item.label)">{{ item.label }}</span>
                </template>
              </UDropdownMenu>
            </div>
            <UFormField label="Workflow Name" name="name" required>
              <UInput id="name" v-model="formData.name" class="flex" placeholder="e.g., Early Check-in Offer" />
            </UFormField>

            <UFormField label="Description" name="description">
              <UTextarea id="description" v-model="formData.description" class="flex"
                placeholder="What does this workflow do?" :rows="2" />
            </UFormField>

            <UFormField label="Workflow Type" name="workflowType">
              <URadioGroup v-model="formData.type" :items="workflowTypeOptions"
                class="grid grid-cols-1 gap-3 sm:grid-cols-3" />
            </UFormField>

            <UFormField label="Trigger Event" name="trigger">
              <USelectMenu v-model="formData.trigger" :items="triggerOptions" placeholder="Select trigger event"
                value-key="value" label-key="label" />
            </UFormField>

            <template v-if="formData.trigger !== 'inquiry'">
              <UFormField label="When" name="triggerTiming.when">
                <USelectMenu v-model="formData.triggerTiming.when" :items="timingOptions" placeholder="Select timing"
                  value-key="value" label-key="label" />
              </UFormField>

              <UFormField label="How many days?" name="triggerDays">
                <UInput id="triggerDays" :model-value="triggerDays" @update:model-value="triggerDays = $event"
                  class="flex" type="number" />
              </UFormField>
            </template>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Listings
            </h3>
          </template>

          <div class="space-y-4">
            <UFormField label="Select Listings" name="listingIds">
              <USelectMenu :model-value="formData.listingIds" :items="listingItems" multiple
                placeholder="Choose which listings this workflow applies to" class="w-full max-w-[240px]"
                label-key="label" value-key="value" @update:model-value="handleListingSelection" />
            </UFormField>

            <div v-if="formData.listingIds.length > 0" class="mt-3">
              <p class="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Selected Listings ({{ formData.listingIds.length }} of {{ listingStore.listings.length }}):
              </p>
              <div class="flex flex-wrap gap-2">
                <UBadge v-for="listingId in formData.listingIds" :key="listingId" variant="soft" color="primary"
                  size="sm">
                  {{ listingStore.listings.find(l => l.id === listingId)?.name || listingId }}
                </UBadge>
              </div>
            </div>
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Message Sequence
            </h3>
          </template>
          <div>
            <WorkflowsNewSimpleWorkflowBuilder :steps="formData.steps" :workflow-type="formData.type"
              @add-step="handleAddStep" @remove-step="handleRemoveStep" @update-step="handleUpdateStep" />
          </div>
        </UCard>

        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold leading-6">
              Settings
            </h3>
          </template>
          <div class="space-y-4">
            <div class="flex items-center space-x-3">
              <USwitch id="active" v-model="formData.active" />
              <label for="active" class="text-sm font-medium text-gray-900 dark:text-white">Activate workflow
                immediately</label>
            </div>
            <div class="flex items-center space-x-3">
              <USwitch id="sms" v-model="formData.sendSms" />
              <label for="sms" class="text-sm font-medium text-gray-900 dark:text-white">Send via SMS</label>
            </div>
            <div class="flex items-center space-x-3">
              <USwitch id="email" v-model="formData.sendOta" />
              <label for="email" class="text-sm font-medium text-gray-900 dark:text-white">Send via OTA</label>
            </div>
            <div class="flex items-center space-x-3">
              <USwitch id="email" v-model="formData.sendEmail" />
              <label for="email" class="text-sm font-medium text-gray-900 dark:text-white">Send via email</label>
            </div>
          </div>
        </UCard>

        <div class="flex items-center justify-end gap-2">
          <UButton icon="i-heroicons-arrow-down-tray-20-solid" :disabled="!isWorkflowValid" @click="handleSave()">
            Create Workflow
          </UButton>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>

<style scoped></style>
