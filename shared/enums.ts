export const CalryPmsIntegrationDefinitionKeys = {
  ownerrez: {
    key: 'ownerrez',
    name: 'OwnerRez Authentication Link',
  },
  ownerrezSandbox: {
    key: 'ownerrez-sandbox',
    name: 'OwnerRez Sandbox Authentication Link',
  },
  hostaway: {
    key: 'hostaway',
    name: 'HostAway Authentication Link',
  },
  hostfully: {
    key: 'hostfully',
    name: 'Hostfully Authentication Link',
  },
  hostfullySandbox: {
    key: 'hostfully-sandbox',
    name: 'Hostfully Sandbox Authentication Link',
  },
  guesty: {
    key: 'guesty',
    name: 'Guesst Authentication Link',
  },
  lodgify: {
    key: 'lodgify',
    name: 'Lodgify Authentication Link',
  },
  beds24: {
    key: 'beds24',
    name: 'Beds24 Authentication Link',
  },
  smoobu: {
    key: 'smoobu',
    name: 'Smoobu Authentication Link',
  },
  uplisting: {
    key: 'uplisting',
    name: 'Uplisting Authentication Link',
  },
  lodgix: {
    key: 'lodgix',
    name: 'Lodgix Authentication Link',
  },
  tokeet: {
    key: 'tokeet',
    name: 'Tokeet Authentication Link',
  },
  hospitable: {
    key: 'hospitable',
    name: 'Hospitable Authentication Link',
  },
  hostex: {
    key: 'hostex',
    name: 'Hostex Authentication Link',
  },
  elina: {
    key: 'elina',
    name: 'Elina Authentication Link',
  },
  yourRentals: {
    key: 'your-rentals',
    name: 'Your Rentals Authentication Link',
  },
  bookingSync: {
    key: 'bookingsync',
    name: 'BookingSync Authentication Link',
  },
  avaiBook: {
    key: 'avaibook',
    name: 'AvaiBook Authentication Link',
  },
  direct: {
    key: 'direct',
    name: 'Direct Authentication Link',
  },
  cloudBeds: {
    key: 'cloudbeds',
    name: 'CloudBeds Authentication Link',
  },
  zeevou: {
    key: 'zeevou',
    name: 'Zeevou Authentication Link',
  },
}

export const calryReservationStatuses = {
  INQUIRY: 'inquiry',
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  MODIFIED: 'modified',
  CANCELLED: 'cancelled',
  EXPIRED: 'expired',
  NO_SHOW: 'no_show',
  STAYING: 'staying',
  POST_STAY: 'post_stay',
  UNKNOWN: 'unknown',
  BLOCKED: 'blocked',
}

export const calryChannelTypes = {
  DIRECT: 'direct',
  WEBSITE: 'website',
  AGODA: 'agoda',
  AIRBNB: 'airbnb',
  ATRAVEODE: 'atraveode',
  BEDANDBREAKFAST: 'bedandbreakfast',
  BOOKEASYCOMAU: 'bookeasycomau',
  BOOKING: 'booking',
  BOOKING_COM: 'booking_com',
  BOOKITCONZ: 'bookitconz',
  DESPEGAR: 'despegar',
  EDREAMSODIGEO: 'edreamsodigeo',
  EXPEDIA: 'expedia',
  FERATEL: 'feratel',
  FLIPKEY: 'flipkey',
  GOIBIBO: 'goibibo',
  GOOGLECAL: 'googlecal',
  GOOGLEADS: 'googleads',
  GUESTLINKCOUK: 'guestlinkcouk',
  HOLIDAYLETTINGSCOUK: 'holidaylettingscouk',
  HOMEAWAY: 'homeaway',
  HOMETOGO: 'hometogo',
  HOSTELINTERNATIONAL: 'hostelinternational',
  HOSTELSCLUB: 'hostelsclub',
  HOSTELWORLD: 'hostelworld',
  HOTELBEDS: 'hotelbeds',
  HOUSETRIPCOM: 'housetripcom',
  HRS: 'hrs',
  ICALIMPORT: 'icalimport',
  JOMRES: 'jomres',
  LASTMINUTE: 'lastminute',
  NZAA: 'nzaa',
  OSTROVOKRU: 'ostrovokru',
  RESERVA: 'reserva',
  REZINTELNET: 'rezintelnet',
  TABLETHOTELS: 'tablethotels',
  TIKET: 'tiket',
  TOMASTRAVEL: 'tomastravel',
  TRAUMFERIENWOHNUNGEN: 'traumferienwohnungen',
  TRAVELOKA: 'traveloka',
  TRAVIA: 'travia',
  TRIP: 'trip',
  TRIPADVISOR: 'tripadvisor',
  TRIPADVISORRENTALS: 'tripadvisorrentals',
  TRIVAGOCOM: 'trivagocom',
  VACATIONSTAY: 'vacationstay',
  VISITSCOTLANDCOM: 'visitscotlandcom',
  VRBO: 'vrbo',
  WEBROOMSCONZ: 'webroomsconz',
  UNKNOWN: 'unknown',
}


