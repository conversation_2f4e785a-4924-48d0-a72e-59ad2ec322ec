export function useApi() {
  const getWorkspace = async () => {
    return await useApiFetch('workspace')
  }
  const getBilling = async () => {
    return await useApiFetch('workspace/billing')
  }
  const getGdpListingLeaderboard = async () => {
    return await useApiFetch(`workspace/yar/gdp-listing-leaderboard`)
  }
  const getCounts = async () => {
    return await useApiFetch('workspace/yar/gdp-counts')
  }
  const getViews = async () => {
    return await useApiFetch('workspace/yar/gdp-views')
  }
  const getSmartSpaces = async () => {
    return await useApiFetch('workspace/yar/gdp-smart-spaces')
  }
  const getContacts = async () => {
    return await useApiFetch('workspace/yar/gdp-contacts')
  }
  const getConversions = async () => {
    return await useApiFetch('workspace/yar/gdp-conversions')
  }
  const getChats = async () => {
    return await useApiFetch('workspace/yar/gdp-chats')
  }
  const getAutopilot = async () => {
    return await useApiFetch('workspace/yar/gdp-autopilot')
  }
  const getListing = async (listingId: string) => {
    return await useApiFetch(`listings/${listingId}`)
  }
  const getTasks = async ({ listingId, conversationId, startAfter }: { listingId?: string, conversationId?: string, startAfter?: string } = {}) => {
    const params = new URLSearchParams()
    if (listingId)
      params.append('listingId', listingId)
    if (conversationId)
      params.append('conversationId', conversationId)
    if (startAfter)
      params.append('startAfter', startAfter)
    const query = params.toString()
    console.log('query', query)
    return await useApiFetch(`tasks${query ? `?${query}` : ''}`)
  }
  const updateTask = async (id: string, payload: any) => {
    return await useApiFetch(`tasks/${id}`, {
      method: 'PATCH',
      body: payload,
    })
  }

  return {
    getWorkspace,
    getBilling,
    getGdpListingLeaderboard,
    getCounts,
    getViews,
    getSmartSpaces,
    getContacts,
    getConversions,
    getChats,
    getAutopilot,
    getListing,
    getTasks,
    updateTask,
  }
}
