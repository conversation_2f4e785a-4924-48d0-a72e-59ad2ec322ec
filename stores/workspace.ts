import { doc, onSnapshot } from 'firebase/firestore'
import { defineStore } from 'pinia'

const { getWorkspace, getBilling, getGdpListingLeaderboard, getCounts, getViews, getSmartSpaces, getContacts, getConversions, getChats, getAutopilot } = useApi()

export const useWorkspaceStore = defineStore('workspace', {
  state: (
  ) => ({
    workspace: null as any | null,
    billing: null as any | null,
    profile: null as any | null,
    unsubscribers: [] as any[],
    counts: null as any | null,
    views: null as any | null,
    smartSpaces: null as any | null,
    listingLeaderboard: null as any | null,
    contacts: null as any | null,
    conversions: [] as any[],
    chats: null as any | null,
    autopilot: null as any | null,
  }),
  actions: {
    annihilate() {
      this.unsubscribers.forEach(unsubscribe => unsubscribe())
      this.unsubscribers = []
      this.workspace = null
      this.profile = null
    },
    async init() {
      const { $db } = useNuxtApp()
      if (!this.workspace || !this.billing) {
        const [workspace, billing] = await Promise.all([
          this.workspace ? Promise.resolve(this.workspace) : getWorkspace(),
          this.billing ? Promise.resolve(this.billing) : getBilling(),
        ])
        this.workspace = workspace
        this.billing = billing
      }
      if (!this.profile) {
        const db = $db
        const user = await getCurrentUser()
        try {
          const unsubscribe = onSnapshot(
            doc(db, 'users', user.uid),
            { includeMetadataChanges: true },
            (userDoc) => {
              this.profile = { ...userDoc.data(), id: userDoc.id }
            },
          )
          this.unsubscribers.push(unsubscribe)
        }
        catch (e) {
        }
      }
    },
    async loadBilling() {
      const billing = await getBilling()
      this.billing = billing
    },
    async loadListingLeaderboard() {
      const toast = useToast()
      try {
        this.listingLeaderboard = await getGdpListingLeaderboard()
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load listing leaderboard',
          color: 'error',
        })
      }
    },
    async loadCounts() {
      const toast = useToast()
      try {
        const { data } = await getCounts()
        this.counts = data
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load GDP counts',
          color: 'error',
        })
      }
    },
    async loadViews() {
      const toast = useToast()
      try {
        const { data } = await getViews()
        this.views = data
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load views',
          color: 'error',
        })
      }
    },
    async loadSmartSpaces() {
      const toast = useToast()
      try {
        const { data } = await getSmartSpaces()
        this.smartSpaces = data
      }
      catch (e) {
        toast.add({
          title: 'Error',
          description: 'Failed to load smart spaces',
          color: 'error',
        })
      }
    },
    async loadContacts() {
      try {
        const { data } = await getContacts()

        const sortData = data => ({
          chatContacts: (data.chatContacts || []).sort((a, b) => b.createdAt - a.createdAt),
          guidebookContacts: (data.guidebookContacts || []).sort((a, b) => b.createdAt - a.createdAt),
          popupContacts: (data.popupContacts || []).sort((a, b) => b.createdAt - a.createdAt),
        })

        this.contacts = {
          recent: sortData(data.recent || {}),
          previous: sortData(data.previous || {}),
        }
      }
      catch (error) {
        console.error('Error loading YAR contacts:', error)
      }
    },
    async loadConversions() {
      const { data } = await getConversions()
      this.conversions = data
    },

    async loadChats() {
      try {
        const { data } = await getChats()
        this.chats = data.data
      }
      catch (e) {
        console.error('Error loading chats:', e)
      }
    },
    async loadAutopilot() {
      const { data } = await getAutopilot
      this.autopilot = data
    },
  },
})
