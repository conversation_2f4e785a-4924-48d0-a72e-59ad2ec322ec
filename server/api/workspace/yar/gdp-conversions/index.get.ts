import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)

  const conversionsSnap = await firestore.collection('conversions').where('userId', '==', user.id).where('createdAt', '>=', Date.now() - 30 * 24 * 60 * 60 * 1000).orderBy('createdAt', 'desc').get()
  const conversions = conversionsSnap.docs.map(doc => ({ ...doc.data(), id: doc.id }))
  return {
    data: conversions,
  }
})
