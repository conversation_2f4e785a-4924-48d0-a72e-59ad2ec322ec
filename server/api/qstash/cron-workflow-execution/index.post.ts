import { firestore } from '~/helpers/firebase'
import type { Workflow } from '~/types'

// Function to convert duration string to milliseconds
function durationToMilliseconds(duration: string): number {
  const match = duration.match(/^(\d+)([sdh])$/)
  if (!match) {
    throw new Error(`Invalid duration format: ${duration}`)
  }

  const value = parseInt(match[1])
  const unit = match[2]

  switch (unit) {
    case 's': // seconds
      return value * 1000
    case 'h': // hours
      return value * 60 * 60 * 1000
    case 'd': // days
      return value * 24 * 60 * 60 * 1000
    default:
      throw new Error(`Unsupported time unit: ${unit}`)
  }
}

export default defineEventHandler(async (event) => {

  const midnightUTC = new Date(Date.now());
  midnightUTC.setUTCHours(0, 0, 0, 0);
  const midnightTimestamp = midnightUTC.getTime();

  console.log('midnight timestamp: ', midnightTimestamp)

  const workflowsSnap = await firestore.collection('workflows').where('active', '==', true).where('type', '==', 'upsell').get()
  const workflows: Workflow[] = workflowsSnap.docs.map(d => ({ ...d.data(), id: d.id } as Workflow))

  console.log('workflows: ', workflows)

  for (const workflow of workflows) {
    // Skip workflows without triggerTiming
    if (!workflow.triggerTiming) {
      console.log(`Workflow ${workflow.name || workflow.id}: skipping - no triggerTiming`)
      continue
    }

    const durationMs = durationToMilliseconds(workflow.triggerTiming.time)
    console.log(`Workflow ${workflow.name || workflow.id}: trigger in ${durationMs}ms`)
  }

  return 'Hello Nitro'
})
