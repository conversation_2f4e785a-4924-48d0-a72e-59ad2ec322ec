import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const workflowSnap: any = await firestore.doc(`workflows/${event.context.params.id}`).get()
  const adminUser = await getAdminUser(event.context.auth.uid)
  if (workflowSnap.data().userId !== adminUser.id) {
    throw createError({ statusCode: 401, message: 'Unauthorized' })
  }
  return { ...workflowSnap.data(), id: workflowSnap.id }
})
