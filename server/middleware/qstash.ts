import { Receiver } from '@upstash/qstash'
import { createError, defineE<PERSON><PERSON><PERSON><PERSON>, getRequestHeader, readBody } from 'h3'

const PROTECTED_ROUTES = ['qqstash']

export default defineEventHandler(async (event) => {
  const url = event.node.req.url
  if (!url) {
    return
  }

  const isProtectedRoute = PROTECTED_ROUTES.some(route => url.startsWith(`/api/${route}`))

  if (!isProtectedRoute) {
    return
  }

  try {
    // Check if QStash signing keys are configured
    if (!process.env.QSTASH_CURRENT_SIGNING_KEY || !process.env.QSTASH_NEXT_SIGNING_KEY) {
      throw createError({
        statusCode: 500,
        statusMessage: 'QStash signing keys not configured',
      })
    }

    // Initialize QStash receiver with signing keys
    const receiver = new Receiver({
      currentSigningKey: process.env.QSTASH_CURRENT_SIGNING_KEY,
      nextSigningKey: process.env.QSTASH_NEXT_SIGNING_KEY,
    })

    // Get the signature from the request header
    // Note: Some platforms (Vercel, Netlify) might receive the header in lowercase
    const signature = getRequestHeader(event, 'upstash-signature')
      || getRequestHeader(event, 'Upstash-Signature')

    if (!signature) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Missing QStash signature header',
      })
    }

    // Get the raw request body
    const body = await readBody(event)
    const bodyString = typeof body === 'string' ? body : JSON.stringify(body)

    // Verify the signature
    const isValid = await receiver.verify({
      signature,
      body: bodyString,
    })

    if (!isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid QStash signature',
      })
    }

    // If verification passes, continue with the request
    console.log('QStash request verified successfully')
  }
  catch (error) {
    console.error('QStash verification failed:', error)

    // If it's already an H3 error, throw it as is
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }

    // Otherwise, create a generic unauthorized error
    throw createError({
      statusCode: 401,
      statusMessage: 'QStash verification failed',
    })
  }
})
