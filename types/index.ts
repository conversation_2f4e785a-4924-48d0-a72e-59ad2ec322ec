export interface Workflow {
    id?: string
    name?: string
    description?: string
    type?: string
    trigger?: string
    triggerTiming?: { time: string, when: string }
    active?: boolean
    sendEmail?: boolean
    sendSms?: boolean
    sendOta?: boolean
    steps?: any[]
    listingIds?: string[]
    userId: string
    createdAt?: number
}

export interface Sop {
    id: string
    title: string
    description: string
    type?: string
    createdAt?: { _seconds: number, _nanoseconds: number } | number
    updatedAt?: { _seconds: number, _nanoseconds: number } | number
}

export interface Popup {
    id: string
    title: string
    subtitle: string
    image: string
    seconds: number
    desktop: boolean
    mobile: boolean
    active: boolean
    userId: string
    createdAt: { _seconds: number, _nanoseconds: number }
}
